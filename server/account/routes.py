from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from agent.account.account_analyzer import ResearchType
from server.account.add_account_to_zoho import add_account_to_crm_zoho
from server.account.analysize_account import analyze_account
from server.account.prospecting_thread_manager import prospecting_thread_manager
from server.common_types import CurrentUser
from utils.logger import get_logger

router = APIRouter(tags=["account"])
logger = get_logger(__name__)


class ProspectingAccountsRequest(BaseModel):
    account_id: str = Field(..., description="The ID of the account")
    user_query: str = Field(..., description="The user_query to use for the account")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class ProspectingMoreRequest(BaseModel):
    thread_id: str = Field(..., description="The thread ID")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class AddAccountRequest(BaseModel):
    user_id: str = Field(..., description="The user ID")
    account_info: dict = Field(..., description="The account info")


class AnalyzeAccountRequest(BaseModel):
    research_type: ResearchType = Field(
        default=ResearchType.RESELLER,
        description="调研类型：reseller 合作伙伴调研 或 direct_customer 直接客户调研"
    )
    user_analysis_requirements: Optional[str] = Field(
        default=None,
        description="用户分析需求：用户明确表达的调研重点和关注方向"
    )


@router.post("/prospecting-accounts")
async def prospecting_accounts(request: ProspectingAccountsRequest):
    """
    Prospecting accounts, start a new prospecting thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    account_id = request.account_id
    user_query = request.user_query
    current_user = request.current_user

    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    if not user_query:
        raise HTTPException(status_code=400, detail="User query is required")

    try:
        # 创建探查流程
        return await prospecting_thread_manager.create_thread(
            account_id=account_id,
            user_query=user_query,
            current_user=current_user,
        )
    except Exception as e:
        logger.error(f"Create prospecting thread failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Create prospecting thread failed: {str(e)}")


@router.post("/prospecting-accounts/more")
async def prospecting_accounts_more(request: ProspectingMoreRequest):
    """
    Prospecting more accounts on existing thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    thread_id = request.thread_id
    current_user = request.current_user
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")

    try:
        # 向探查流程中添加新的探查任务
        return await prospecting_thread_manager.add_thread_task(thread_id, current_user)
    except Exception as e:
        logger.error(f"Add prospecting thread task failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Add prospecting thread task failed: {str(e)}")


@router.get("/prospecting-accounts/{account_id}/threads")
async def get_prospecting_accounts_history(account_id: str):
    """
    Get the history of prospecting accounts
    """
    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    return await prospecting_thread_manager.list_all_prospecting_threads(account_id)


@router.get("/prospecting-accounts/thread/{thread_id}")
async def get_prospecting_accounts_task_profile(thread_id: str):
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")
    try:
        return await prospecting_thread_manager.get_prospecting_thread_data(thread_id)
    except Exception as e:
        logger.error(f"Get prospecting data failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Get prospecting data failed: {str(e)}")


@router.post("/add-account")
async def add_account(request: AddAccountRequest):
    """Add a account to crm zoho"""
    logger.debug(f"收到添加account请求，request: {request.model_dump()}")

    user_id = request.user_id
    account_info = request.account_info

    if not user_id:
        raise HTTPException(status_code=400, detail="User ID is required")

    if not account_info:
        raise HTTPException(status_code=400, detail="Account info is required")

    try:
        # 添加公司到 crm zoho
        account_id = await add_account_to_crm_zoho(account_info, user_id)
        return {"account_id": account_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/analyze-account/{account_id}")
async def analyze_account_by_id(account_id: str, request: AnalyzeAccountRequest):
    """Analyze a account with specified research type and requirements"""
    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    return await analyze_account.run(
        account_id=account_id,
        research_type=request.research_type,
        user_analysis_requirements=request.user_analysis_requirements
    )


@router.get("/analyze-account/{account_id}")
async def get_analyze_account_result(account_id: str):
    """Get the analyze account result by account id"""
    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    return await analyze_account.get_analyze_account_result(account_id)
