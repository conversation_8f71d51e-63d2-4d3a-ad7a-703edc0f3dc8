import asyncio
import uuid
from datetime import datetime

from loguru import logger
from pymongo.collection import Collection

from agent.account.account_analyzer import analyze_account as analyze_account_agent
from server.task.mongodb_client import mongodb_client
from utils.env import load_env
from zoho.accounts_api import fetch_accounts_info_by_account_id


class AnalyzeAccount:
    """
    AnalyzeAccount is a class that analyzes the account.
    """

    def __init__(self):
        """
        Initialize the MongoDB client.
        """
        self.db = mongodb_client.db
        self.collection: Collection = self.db["account_analyzer"]
        self._setup_indexes()

    def _setup_indexes(self):
        """
        Setup the index for the collection.
        """
        try:
            self.collection.create_index("account_id")
            self.collection.create_index("report")
            self.collection.create_index("created_at")
        except Exception as e:
            logger.error(f"Error setting up indexes: {e}")

    async def run(self, account_id: str):
        """
        Run the account analyzer by account id.
        """
        # query the account info from zoho
        account_info = await fetch_accounts_info_by_account_id(account_id)
        if not account_info:
            return None

        # analyze the account info
        report_content = await analyze_account_agent(account_info)
        if not report_content:
            return None

        # save the result to the database
        report_data = {
            "_id": str(uuid.uuid4()).replace("-", ""),
            "account_id": account_id,
            "report": report_content,
            "created_at": datetime.now(),
        }
        self.collection.insert_one(report_data)

        return report_data

    async def get_analyze_account_result(self, account_id: str):
        """
        Get the analyze account result by account id.
        """
        # 查询 account_id，按 created_at 倒序，取最新一条
        result = self.collection.find_one({"account_id": account_id}, sort=[("created_at", -1)])
        if not result:
            return None
        return result


analyze_account = AnalyzeAccount()


if __name__ == "__main__":

    async def main():
        load_env()
        # query the account info from zoho
        account_info = await fetch_accounts_info_by_account_id("3091799000303143016")
        logger.info("account_info: {}", account_info)
        if not account_info:
            return None

        # analyze the account info
        report_content = await analyze_account_agent(account_info)
        if not report_content:
            return None

        logger.info("Final report content: {}", report_content)
        return report_content

    asyncio.run(main())
