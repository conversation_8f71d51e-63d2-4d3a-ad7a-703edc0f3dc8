from enum import Enum
from typing import Optional

from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import ZohoAccountInfo


class ResearchType(str, Enum):
    """调研类型枚举"""
    RESELLER = "reseller"
    DIRECT_CUSTOMER = "direct_customer"


def _format_crm_info(account_info: ZohoAccountInfo) -> str:
    """
    格式化 CRM 信息为易读的 Markdown 格式

    Args:
        account_info: ZohoAccountInfo 对象

    Returns:
        str: 格式化后的 CRM 信息
    """
    formatted_info = []

    # 基本信息
    if account_info.get('name'):
        formatted_info.append(f"- **公司名称**: {account_info['name']}")

    if account_info.get('website'):
        formatted_info.append(f"- **官方网站**: {account_info['website']}")

    if account_info.get('industry'):
        formatted_info.append(f"- **行业**: {account_info['industry']}")

    if account_info.get('stage'):
        formatted_info.append(f"- **销售阶段**: {account_info['stage']}")

    if account_info.get('priority'):
        formatted_info.append(f"- **优先级**: {account_info['priority']}")

    # 地址信息
    address_parts = []
    if account_info.get('billing_state'):
        address_parts.append(account_info['billing_state'])

    if account_info.get('billing_address'):
        billing_addr = account_info['billing_address']
        if isinstance(billing_addr, dict):
            if billing_addr.get('state'):
                address_parts.append(billing_addr['state'])
            if billing_addr.get('region'):
                address_parts.append(billing_addr['region'])

    if address_parts:
        formatted_info.append(f"- **地址**: {', '.join(address_parts)}")

    # 市场细分
    if account_info.get('market_segments'):
        segments = account_info['market_segments']
        if isinstance(segments, list) and segments:
            formatted_info.append(f"- **市场细分**: {', '.join(segments)}")

    # 销售来源
    if account_info.get('lead_source'):
        formatted_info.append(f"- **线索来源**: {account_info['lead_source']}")

    # 负责人信息
    if account_info.get('owner'):
        owner = account_info['owner']
        if isinstance(owner, dict) and owner.get('name'):
            formatted_info.append(f"- **负责人**: {owner['name']}")

    # 创建和修改时间
    if account_info.get('created_time'):
        formatted_info.append(f"- **创建时间**: {account_info['created_time']}")

    if account_info.get('last_activity_time'):
        formatted_info.append(f"- **最后活动时间**: {account_info['last_activity_time']}")

    # 描述信息
    if account_info.get('description'):
        formatted_info.append(f"- **描述**: {account_info['description']}")

    # 标签
    if account_info.get('tag'):
        tags = account_info['tag']
        if isinstance(tags, list) and tags:
            formatted_info.append(f"- **标签**: {', '.join(tags)}")

    # 区域信息
    if account_info.get('territory'):
        formatted_info.append(f"- **区域**: {account_info['territory']}")

    if account_info.get('territories'):
        territories = account_info['territories']
        if isinstance(territories, list) and territories:
            territory_names = []
            for territory in territories:
                if isinstance(territory, dict) and territory.get('name'):
                    territory_names.append(territory['name'])
            if territory_names:
                formatted_info.append(f"- **覆盖区域**: {', '.join(territory_names)}")

    if not formatted_info:
        return "暂无详细的 CRM 信息"

    return "\n".join(formatted_info)


async def analyze_account(
    account_info: ZohoAccountInfo,
    research_type: ResearchType = ResearchType.RESELLER,
    user_analysis_requirements: Optional[str] = None
) -> str:
    """
    Analyze the account info and return a markdown formatted analysis report.

    Args:
        account_info: ZohoAccountInfo object containing company information
        research_type: Type of research (reseller or direct_customer)
        user_analysis_requirements: User's specific analysis requirements and focus areas

    Returns:
        str: Markdown formatted analysis report in English
    """
    # create llm agent to analyze the account info

    tools = [tavily_search, tavily_crawl, firecrawl_scrape, get_company_details]

    llm = init_model(
        model="gemini-2.5-flash",
        max_tokens=10240,
        temperature=0.2,
        thinking_budget=256,
        include_thoughts=True,
    )

    llm.bind_tools(tools)

    # 加载分析 prompt
    system_prompt = load_file("agent/account/prompts/analysis_company.md")

    # 加载映翰通公司简介
    inhand_business_card = load_file("prompts/inhand_business_card.md")

    if not system_prompt:
        raise ValueError("system_prompt is None")

    if not inhand_business_card:
        raise ValueError("inhand_business_card is None")

    # 将映翰通简介插入到系统提示词中
    system_prompt = system_prompt.format(inhand_business_card=inhand_business_card)

    # 格式化 CRM 信息为更易读的格式
    formatted_crm_info = _format_crm_info(account_info)

    # 确定调研类型的中文描述
    research_type_cn = "reseller 合作伙伴调研" if research_type == ResearchType.RESELLER else "直接客户调研"

    # 构建结构化的输入信息
    structured_input = f"""## 调研目标公司信息

### 基本信息
- **公司名称**: {account_info.get('name', '未知')}
- **调研类型**: {research_type_cn}

### 从公司 CRM 获取的信息
{formatted_crm_info}

### 用户分析需求
{user_analysis_requirements or '无特殊要求，请按照标准流程进行全面分析'}
"""

    # 构建提示词
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template("{structured_input}"),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke({"structured_input": structured_input})

    # 直接提取并返回文本内容
    if hasattr(result, "content"):
        if isinstance(result.content, list):
            # 过滤掉 thinking 类型的内容，只保留文本内容
            text_content = []
            for item in result.content:
                if isinstance(item, dict) and item.get("type") == "text":
                    text_content.append(item.get("text", ""))
                elif isinstance(item, str):
                    text_content.append(item)
            return "\n".join(text_content)
        else:
            return str(result.content)
    else:
        return str(result)
