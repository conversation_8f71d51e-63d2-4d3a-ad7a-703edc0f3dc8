from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import ZohoAccountInfo


async def analyze_account(account_info: ZohoAccountInfo) -> str:
    """
    Analyze the account info and return a markdown formatted analysis report.

    Args:
        account_info: ZohoAccountInfo object containing company information
        research_type: Type of research (reseller or direct_customer)
        user_analysis_requirements: User's specific analysis requirements and focus areas

    Returns:
        str: Markdown formatted analysis report in English
    """
    # create llm agent to analyze the account info

    tools = [tavily_search, tavily_crawl, firecrawl_scrape, get_company_details]

    llm = init_model(
        model="gemini-2.5-flash",
        max_tokens=10240,
        temperature=0.2,
        thinking_budget=256,
        include_thoughts=True,
    )

    llm.bind_tools(tools)

    # 加载分析 prompt
    system_prompt = load_file("agent/account/prompts/analysis_company.md")

    # 加载映翰通公司简介
    inhand_business_card = load_file("prompts/inhand_business_card.md")

    if not system_prompt:
        raise ValueError("system_prompt is None")

    if not inhand_business_card:
        raise ValueError("inhand_business_card is None")

    # 将映翰通简介插入到系统提示词中
    system_prompt = system_prompt.format(inhand_business_card=inhand_business_card)

    # 构建输入信息，按照新 prompt 的要求格式化
    company_name = account_info.get('name', '未知')
    region = account_info.get('billing_state', '未知')

    # 简单格式化 CRM 信息
    crm_info_parts = []
    if account_info.get('website'):
        crm_info_parts.append(f"官网: {account_info['website']}")
    if account_info.get('industry'):
        crm_info_parts.append(f"行业: {account_info['industry']}")
    if account_info.get('stage'):
        crm_info_parts.append(f"销售阶段: {account_info['stage']}")
    if account_info.get('description'):
        crm_info_parts.append(f"描述: {account_info['description']}")

    crm_info = "; ".join(crm_info_parts) if crm_info_parts else "暂无详细信息"

    # 按照 prompt 要求的格式构建输入
    structured_input = f"公司名称: {company_name}, 地区: {region}, 从公司CRM获取到的信息: {crm_info}"

    # 构建提示词
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template("{structured_input}"),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke({"structured_input": structured_input})

    # 直接提取并返回文本内容
    if hasattr(result, "content"):
        if isinstance(result.content, list):
            # 过滤掉 thinking 类型的内容，只保留文本内容
            text_content = []
            for item in result.content:
                if isinstance(item, dict) and item.get("type") == "text":
                    text_content.append(item.get("text", ""))
                elif isinstance(item, str):
                    text_content.append(item)
            return "\n".join(text_content)
        else:
            return str(result.content)
    else:
        return str(result)
