from typing import Optional

from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import ZohoAccountInfo


async def analyze_account(
    account_info: ZohoAccountInfo, analyze_type: Optional[str] = None, user_input: Optional[str] = None
) -> str:
    """
    Analyze the account info and return a markdown formatted analysis report.

    Args:
        account_info: ZohoAccountInfo object containing company information
        analyze_type: "reseller" or "direct_customer"
        user_input: User's specific analysis requirements and focus areas

    Returns:
        str: Markdown-formatted analysis report
    """
    # create llm agent to analyze the account info

    tools = [tavily_search, tavily_crawl, firecrawl_scrape, get_company_details]

    llm = init_model(
        model="gemini-2.5-pro",
        max_tokens=10240,
        temperature=0.1,
        thinking_budget=256,
        include_thoughts=True,
    )

    llm.bind_tools(tools)

    # 加载分析 prompt
    system_prompt = load_file("agent/account/prompts/analysis_company.md")

    # 加载映翰通公司简介
    inhand_business_card = load_file("prompts/inhand_business_card.md")

    if not system_prompt:
        raise ValueError("system_prompt is None")

    if not inhand_business_card:
        raise ValueError("inhand_business_card is None")

    # 将映翰通简介插入到系统提示词中
    system_prompt = system_prompt.format(inhand_business_card=inhand_business_card)

    # 构建提示词
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template(
                "账户信息: {account_info}\n分析类型: {analyze_type}\n用户需求: {user_input}"
            ),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke(
        {
            "account_info": account_info,
            "analyze_type": analyze_type or "reseller",
            "user_input": user_input or "请按照标准流程进行全面分析",
        }
    )

    # 直接提取并返回文本内容
    if hasattr(result, "content"):
        if isinstance(result.content, list):
            # 过滤掉 thinking 类型的内容，只保留文本内容
            text_content = []
            for item in result.content:
                if isinstance(item, dict) and item.get("type") == "text":
                    text_content.append(item.get("text", ""))
                elif isinstance(item, str):
                    text_content.append(item)
            return "\n".join(text_content)
        else:
            return str(result.content)
    else:
        return str(result)


if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    from loguru import logger
    from zoho.accounts_api import fetch_accounts_info_by_account_id

    load_dotenv()

    async def reseller_analyze():
        # 分析 AT&T(3091799000001974023) 成为 reseller 的潜力
        user_input = "请帮我分析 AT&T 成为 reseller 的潜力"
        account_info = await fetch_accounts_info_by_account_id("3091799000001974023")
        if not account_info:
            logger.error("account_info is None")
            return
        logger.info("AT&T 账户信息: {}", account_info)
        result = await analyze_account(account_info=account_info, analyze_type="reseller", user_input=user_input)
        logger.info("AT&T 成为 reseller 的潜力分析结果: {}", result)

    async def direct_customer_analyze():
        # 分析 Sullair Australia (3091799000303143016) 成为直接客户的潜力
        user_input = "请帮我分析 Sullair Australia 成为直接客户的潜力"
        account_info = await fetch_accounts_info_by_account_id("3091799000303143016")
        if not account_info:
            logger.error("account_info is None")
            return
        logger.info("Sullair Australia 账户信息: {}", account_info)
        result = await analyze_account(account_info=account_info, analyze_type="direct_customer", user_input=user_input)
        logger.info("Sullair Australia 成为直接客户的潜力分析结果: {}", result)

    if __name__ == "__main__":
        asyncio.run(direct_customer_analyze())
