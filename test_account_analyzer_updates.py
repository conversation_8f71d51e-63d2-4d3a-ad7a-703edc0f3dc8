#!/usr/bin/env python3
"""
测试 account_analyzer 的更新功能
"""

import asyncio
from agent.account.account_analyzer import ResearchType, _format_crm_info
from zoho.accounts_api import ZohoAccountInfo


def test_research_type_enum():
    """测试调研类型枚举"""
    print("=== 测试调研类型枚举 ===")
    
    # 测试枚举值
    assert ResearchType.RESELLER == "reseller"
    assert ResearchType.DIRECT_CUSTOMER == "direct_customer"
    
    print("✅ ResearchType.RESELLER:", ResearchType.RESELLER)
    print("✅ ResearchType.DIRECT_CUSTOMER:", ResearchType.DIRECT_CUSTOMER)
    print()


def test_format_crm_info():
    """测试 CRM 信息格式化功能"""
    print("=== 测试 CRM 信息格式化 ===")
    
    # 创建测试数据
    test_account_info: ZohoAccountInfo = {
        "id": "test_123",
        "name": "测试公司",
        "website": "https://test-company.com",
        "industry": "工业自动化",
        "stage": "Prospect",
        "priority": "High",
        "billing_state": "上海",
        "market_segments": ["Automation", "IoT"],
        "lead_source": "AI Research",
        "territory": "中国",
        "description": "这是一家专注于工业自动化的公司",
        "created_time": "2024-01-01T00:00:00Z",
        "last_activity_time": "2024-01-15T10:30:00Z",
        "tag": ["重要客户", "潜在合作伙伴"],
        "owner": {
            "name": "张三",
            "id": "owner_123",
            "email": "<EMAIL>"
        }
    }
    
    # 测试格式化功能
    formatted_info = _format_crm_info(test_account_info)
    
    print("格式化后的 CRM 信息：")
    print(formatted_info)
    print()
    
    # 验证关键信息是否包含
    assert "测试公司" in formatted_info
    assert "https://test-company.com" in formatted_info
    assert "工业自动化" in formatted_info
    assert "Prospect" in formatted_info
    assert "High" in formatted_info
    assert "上海" in formatted_info
    assert "张三" in formatted_info
    
    print("✅ CRM 信息格式化测试通过")
    print()


def test_empty_crm_info():
    """测试空 CRM 信息的处理"""
    print("=== 测试空 CRM 信息处理 ===")
    
    # 创建最小测试数据（只有必需字段）
    minimal_account_info: ZohoAccountInfo = {
        "id": "minimal_123",
        "name": "最小公司"
    }
    
    formatted_info = _format_crm_info(minimal_account_info)
    
    print("最小 CRM 信息格式化结果：")
    print(formatted_info)
    print()
    
    assert "最小公司" in formatted_info
    print("✅ 空 CRM 信息处理测试通过")
    print()


async def test_analyze_account_signature():
    """测试 analyze_account 函数签名"""
    print("=== 测试 analyze_account 函数签名 ===")
    
    from agent.account.account_analyzer import analyze_account
    import inspect
    
    # 获取函数签名
    sig = inspect.signature(analyze_account)
    params = list(sig.parameters.keys())
    
    print("analyze_account 函数参数：", params)
    
    # 验证参数
    expected_params = ["account_info", "research_type", "user_analysis_requirements"]
    for param in expected_params:
        assert param in params, f"缺少参数: {param}"
    
    print("✅ analyze_account 函数签名测试通过")
    print()


def main():
    """运行所有测试"""
    print("开始测试 account_analyzer 更新功能...\n")
    
    try:
        # 运行同步测试
        test_research_type_enum()
        test_format_crm_info()
        test_empty_crm_info()
        
        # 运行异步测试
        asyncio.run(test_analyze_account_signature())
        
        print("🎉 所有测试通过！account_analyzer 更新功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
